# 指定人员下单功能接口文档

## 功能概述

指定人员下单功能允许用户在下单时选择特定的员工来提供服务。该功能包含以下主要特性：

1. **员工列表查询**：用户可以查看可选择的员工列表
2. **指定员工下单**：下单时可以指定特定员工
3. **专属接单**：指定员工的订单只有该员工可以看到并接单

## 接口列表

### 1. 获取可选择的员工列表

**接口地址：** `GET /customers/{customerId}/employees`
**接口描述：** 获取用户可以选择的员工列表
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| serviceTypeId | number | 否 | 服务类型ID，用于筛选特定服务类型的员工 |
| current | number | 否 | 当前页码，默认为1 |
| pageSize | number | 否 | 每页大小，默认为10 |

**请求示例：**
```
GET /customers/123/employees?serviceTypeId=1&current=1&pageSize=10
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "张师傅",
        "avatar": "https://example.com/avatar1.jpg",
        "level": 5,
        "workExp": 36,
        "rating": 4.8,
        "phone": "13800138001",
        "vehicle": {
          "id": 1,
          "plateNumber": "京A12345",
          "vehicleType": "面包车",
          "status": "空闲"
        }
      },
      {
        "id": 2,
        "name": "李师傅",
        "avatar": "https://example.com/avatar2.jpg",
        "level": 4,
        "workExp": 24,
        "rating": 4.6,
        "phone": "13800138002",
        "vehicle": null
      }
    ],
    "total": 15,
    "current": 1,
    "pageSize": 10
  }
}
```

### 2. 创建指定员工的订单

**接口地址：** `POST /customers/{customerId}/order`
**接口描述：** 创建订单，支持指定员工
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |

**请求体：**
```json
{
  "employeeId": 1,
  "orderTime": "2024-01-01T10:00:00.000Z",
  "serviceTime": "2024-01-01T14:00:00.000Z",
  "originalPrice": 100.00,
  "totalFee": 80.00,
  "cardDeduction": 20.00,
  "couponDeduction": 0.00,
  "orderDetails": [
    {
      "serviceId": 1,
      "serviceName": "宠物洗护",
      "servicePrice": 100.00,
      "petId": 1,
      "petName": "小白",
      "petType": "狗",
      "petBreed": "金毛",
      "orderTime": "2024-01-01T10:00:00.000Z",
      "addServiceIds": [1, 2]
    }
  ],
  "discountInfos": [
    {
      "discountType": "membership_card",
      "discountId": 1,
      "discountAmount": 20.00
    }
  ]
}
```

**字段说明：**
- `employeeId`：可选字段，如果指定则该订单只有指定员工可以接单
- 其他字段与普通订单创建接口相同

### 3. 员工端查询可接单列表（修改）

**接口地址：** `GET /orders/{userId}`
**接口描述：** 查询员工可接单列表，支持员工ID过滤
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | number | 是 | 用户ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码 |
| pageSize | number | 否 | 每页大小 |
| type | string | 否 | 服务类型 |
| employeeId | number | 否 | 员工ID，如果提供则只显示指定给该员工的订单和未指定员工的订单 |

**请求示例：**
```
GET /orders/123?employeeId=1&current=1&pageSize=10
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1001,
        "sn": "1704067200001234",
        "customerId": 123,
        "employeeId": 1,
        "status": "待接单",
        "orderTime": "2024-01-01T10:00:00.000Z",
        "serviceTime": "2024-01-01T14:00:00.000Z",
        "totalFee": 80.00,
        "customer": {
          "id": 123,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "orderDetails": [
          {
            "id": 1,
            "serviceName": "宠物洗护",
            "servicePrice": 100.00,
            "petName": "小白",
            "petType": "狗",
            "petBreed": "金毛"
          }
        ]
      }
    ],
    "total": 5
  }
}
```

## 业务逻辑说明

### 订单分配逻辑

1. **指定员工订单**：
   - 当用户下单时指定了`employeeId`，该订单状态为"待接单"
   - 只有指定的员工可以在接单大厅看到该订单
   - 其他员工无法看到指定给别人的订单

2. **系统派单订单**：
   - 当用户下单时未指定`employeeId`（或为null），该订单为系统派单
   - 所有员工都可以在接单大厅看到该订单
   - 遵循先到先得的原则

3. **员工查询逻辑**：
   - 如果员工查询时提供了`employeeId`参数，返回：
     - 指定给该员工的订单
     - 未指定员工的订单（系统派单）
   - 如果员工查询时未提供`employeeId`参数，只返回未指定员工的订单

### 员工列表排序规则

员工列表按以下优先级排序：
1. 服务评分（降序）
2. 工作经验（降序）
3. 接单等级（降序）

### 注意事项

1. 只有状态为"启用"的员工才会出现在可选择列表中
2. 指定员工下单后，接单流程与普通订单相同
3. 员工可以拒绝接单，订单会重新进入待接单状态
4. 系统管理员可以进行派单和转单操作

## 测试接口（开发环境）

为了方便测试，提供了无需认证的测试接口：

### 获取员工列表（测试）
```
GET /openapi/employees?current=1&pageSize=10
```

### 查询订单列表（测试）
```
GET /openapi/orders?current=1&pageSize=10&employeeId=1
```

## 前端集成示例

### 1. 获取员工列表
```javascript
const getEmployees = async (customerId, serviceTypeId) => {
  const response = await fetch(`/customers/${customerId}/employees?serviceTypeId=${serviceTypeId}&current=1&pageSize=10`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};
```

### 2. 创建指定员工订单
```javascript
const createOrderWithEmployee = async (customerId, orderData) => {
  const response = await fetch(`/customers/${customerId}/order`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      ...orderData,
      employeeId: selectedEmployeeId // 用户选择的员工ID
    })
  });
  return response.json();
};
```

### 3. 员工查询可接单列表
```javascript
const getAvailableOrders = async (employeeId) => {
  const response = await fetch(`/orders/1?employeeId=${employeeId}&current=1&pageSize=10`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};
```
